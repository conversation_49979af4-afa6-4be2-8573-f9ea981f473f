'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';

const USER_ID_KEY = 'infauna_user_id';

export function useUserId() {
  const { user } = useAuth();
  const [guestUserId, setGuestUserId] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // If user is authenticated, use their auth ID
      if (user?.id) {
        setGuestUserId(null);
        // Remove guest ID from localStorage when user logs in
        localStorage.removeItem(USER_ID_KEY);
        return;
      }

      // For guest users, get or create a persistent ID
      let storedUserId = localStorage.getItem(USER_ID_KEY);
      if (!storedUserId) {
        storedUserId = crypto.randomUUID();
        localStorage.setItem(USER_ID_KEY, storedUserId);
      }
      setGuestUserId(storedUserId);
    }
  }, [user]);

  // Return the appropriate user ID
  const userId = user?.id || guestUserId;
  const isAuthenticated = !!user;
  const isGuest = !user && !!guestUserId;

  return {
    userId,
    isAuthenticated,
    isGuest,
    clearGuestId: () => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(USER_ID_KEY);
        setGuestUserId(null);
      }
    },
  };
}
