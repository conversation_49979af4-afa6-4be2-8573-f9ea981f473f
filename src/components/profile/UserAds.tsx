'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Plus, Edit, Trash2, Eye, MapPin, Calendar, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { useUserId } from '@/hooks/useUserId';

interface AdImage {
  id: number;
  imageUrl: string;
  order: number;
}

interface UserAd {
  id: number;
  title: string;
  adType: string;
  country: string;
  priceType?: string;
  priceAmount?: string;
  description: string;
  contactPhone: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  slug: string;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  breed?: {
    id: number;
    name: string;
    slug: string;
  };
  region?: {
    id: number;
    name: string;
    slug: string;
  };
  images: AdImage[];
}

interface UserAdsResponse {
  ads: UserAd[];
  count: number;
}

export function UserAds() {
  const { userId, isAuthenticated, isGuest } = useUserId();
  const [ads, setAds] = useState<UserAd[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchUserAds();
    }
  }, [userId]);

  const fetchUserAds = async () => {
    if (!userId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}/ads`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch ads');
      }

      const data: UserAdsResponse = await response.json();
      setAds(data.ads);
    } catch (err) {
      console.error('Error fetching user ads:', err);
      setError('Nepodařilo se načíst inzeráty');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAd = async (adId: number) => {
    if (!userId || !confirm('Opravdu chcete smazat tento inzerát?')) return;

    try {
      const response = await fetch(`/api/ads/${adId}?userId=${userId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete ad');
      }

      // Remove ad from local state
      setAds(prev => prev.filter(ad => ad.id !== adId));
    } catch (err) {
      console.error('Error deleting ad:', err);
      alert('Nepodařilo se smazat inzerát');
    }
  };

  const handleToggleActive = async (adId: number, currentStatus: boolean) => {
    if (!userId) return;

    try {
      const response = await fetch(`/api/ads/${adId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update ad status');
      }

      // Update ad status in local state
      setAds(prev => prev.map(ad =>
        ad.id === adId
          ? { ...ad, isActive: !currentStatus }
          : ad
      ));
    } catch (err) {
      console.error('Error updating ad status:', err);
      alert('Nepodařilo se změnit stav inzerátu');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatPrice = (priceType?: string, priceAmount?: string) => {
    if (!priceType) return null;
    
    if (priceType === 'Dohodou' || priceType === 'V textu') {
      return priceType;
    }
    
    if (priceAmount && priceType) {
      return `${priceAmount} ${priceType}`;
    }
    
    return priceType;
  };



  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Moje inzeráty
          </h3>
        </div>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Načítání inzerátů...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Moje inzeráty
          </h3>
        </div>
        <div className="p-6">
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchUserAds}>
              Zkusit znovu
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Moje inzeráty ({ads.length})
          </h3>
          <Button asChild size="sm">
            <Link href="/pridat-inzerat">
              <Plus className="w-4 h-4 mr-2" />
              Přidat inzerát
            </Link>
          </Button>
        </div>
      </div>

      <div className="p-6">
        {ads.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              Zatím nemáte žádné inzeráty
            </h4>
            <p className="text-gray-600 mb-6">
              Začněte přidáním vašeho prvního inzerátu
            </p>
            <Button asChild>
              <Link href="/pridat-inzerat">
                <Plus className="w-4 h-4 mr-2" />
                Přidat první inzerát
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {ads.map((ad) => (
              <div key={ad.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-start space-x-4">
                      {/* Image */}
                      <div className="w-20 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        {ad.images.length > 0 ? (
                          <img
                            src={ad.images[0].imageUrl}
                            alt={ad.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Eye className="w-6 h-6 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 mb-1 truncate">
                          {ad.title}
                        </h4>
                        
                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-xs font-medium mr-2">
                            {ad.adType}
                          </span>
                          <span>{ad.category.name}</span>
                          {ad.breed && (
                            <>
                              <span className="mx-1">•</span>
                              <span>{ad.breed.name}</span>
                            </>
                          )}
                        </div>

                        <div className="flex items-center text-sm text-gray-500 mb-2">
                          <MapPin className="w-4 h-4 mr-1" />
                          <span>{ad.country}</span>
                          {ad.region && (
                            <>
                              <span className="mx-1">•</span>
                              <span>{ad.region.name}</span>
                            </>
                          )}
                        </div>

                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="w-4 h-4 mr-1" />
                          <span>Vytvořeno {formatDate(ad.createdAt)}</span>
                          {formatPrice(ad.priceType, ad.priceAmount) && (
                            <>
                              <span className="mx-2">•</span>
                              <span className="font-medium text-gray-900">
                                {formatPrice(ad.priceType, ad.priceAmount)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 ml-4">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/inzerat/${ad.slug}`}>
                        <Eye className="w-4 h-4 mr-1" />
                        Zobrazit
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/upravit-inzerat/${ad.id}`}>
                        <Edit className="w-4 h-4 mr-1" />
                        Upravit
                      </Link>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteAd(ad.id)}
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Smazat
                    </Button>
                  </div>
                </div>

                {!ad.isActive && (
                  <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-800">
                      Tento inzerát není aktivní
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
